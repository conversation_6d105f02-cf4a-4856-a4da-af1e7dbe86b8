-- Project Database Schema
-- This file recreates the database structure for the venue management system

-- <PERSON>reate database
CREATE DATABASE IF NOT EXISTS `project` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
USE `project`;

-- Table structure for table `users`
CREATE TABLE IF NOT EXISTS `users` (
  `user_id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','lecturer','student') NOT NULL,
  `full_name` varchar(255) NOT NULL,
  `firstname` varchar(100) DEFAULT NULL,
  `lastname` varchar(100) DEFAULT NULL,
  `course` varchar(100) DEFAULT NULL,
  `year` varchar(10) DEFAULT NULL,
  `stream` varchar(10) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  <PERSON>IMARY KEY (`user_id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table structure for table `venues`
CREATE TABLE IF NOT EXISTS `venues` (
  `venueid` int(11) NOT NULL AUTO_INCREMENT,
  `venuename` varchar(255) NOT NULL,
  `capacity` int(11) NOT NULL,
  `location` varchar(255) NOT NULL,
  `venuedescription` text DEFAULT NULL,
  `features` text DEFAULT NULL,
  `status` enum('available','occupied','maintenance','free') DEFAULT 'available',
  `qr_code_path` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`venueid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table structure for table `venue_checkins`
CREATE TABLE IF NOT EXISTS `venue_checkins` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `venue_id` int(11) NOT NULL,
  `check_in_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `check_out_time` timestamp NULL DEFAULT NULL,
  `purpose` varchar(255) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `venue_id` (`venue_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  FOREIGN KEY (`venue_id`) REFERENCES `venues` (`venueid`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table structure for table `timetables`
CREATE TABLE IF NOT EXISTS `timetables` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `course` varchar(100) NOT NULL,
  `year` int(11) NOT NULL,
  `stream` varchar(10) NOT NULL,
  `day` varchar(20) NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `type` varchar(50) NOT NULL,
  `subject_code` varchar(100) NOT NULL,
  `venue` varchar(255) NOT NULL,
  `lecturer` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table structure for table `events`
CREATE TABLE IF NOT EXISTS `events` (
  `event_id` int(11) NOT NULL AUTO_INCREMENT,
  `event_name` varchar(255) NOT NULL,
  `event_description` text DEFAULT NULL,
  `event_date` date NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `venue_id` int(11) DEFAULT NULL,
  `target_audience` enum('all','specific') DEFAULT 'all',
  `target_course` varchar(100) DEFAULT 'all',
  `target_year` varchar(10) DEFAULT 'all',
  `target_stream` varchar(10) DEFAULT 'all',
  `course` varchar(100) DEFAULT 'All',
  `year` varchar(10) DEFAULT 'All',
  `stream` varchar(10) DEFAULT 'All',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`event_id`),
  KEY `venue_id` (`venue_id`),
  KEY `created_by` (`created_by`),
  FOREIGN KEY (`venue_id`) REFERENCES `venues` (`venueid`) ON DELETE SET NULL,
  FOREIGN KEY (`created_by`) REFERENCES `users` (`user_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table structure for table `issues`
CREATE TABLE IF NOT EXISTS `issues` (
  `issue_id` int(11) NOT NULL AUTO_INCREMENT,
  `venue_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `issue_title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `issue_type` varchar(50) NOT NULL,
  `status` enum('pending','in_progress','resolved','closed') DEFAULT 'pending',
  `reported_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `resolved_date` datetime DEFAULT NULL,
  `resolved_by` int(11) DEFAULT NULL,
  `resolution_notes` text DEFAULT NULL,
  PRIMARY KEY (`issue_id`),
  KEY `venue_id` (`venue_id`),
  KEY `user_id` (`user_id`),
  KEY `resolved_by` (`resolved_by`),
  FOREIGN KEY (`venue_id`) REFERENCES `venues` (`venueid`) ON DELETE SET NULL,
  FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE SET NULL,
  FOREIGN KEY (`resolved_by`) REFERENCES `users` (`user_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table structure for table `login_logs`
CREATE TABLE IF NOT EXISTS `login_logs` (
  `log_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `login_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  PRIMARY KEY (`log_id`),
  KEY `user_id` (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table structure for table `reports`
CREATE TABLE IF NOT EXISTS `reports` (
  `report_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `venue_id` int(11) NOT NULL,
  `description` text NOT NULL,
  `status` enum('pending','resolved','dismissed') DEFAULT 'pending',
  `reported_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `resolved_at` timestamp NULL DEFAULT NULL,
  `resolved_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`report_id`),
  KEY `user_id` (`user_id`),
  KEY `venue_id` (`venue_id`),
  KEY `resolved_by` (`resolved_by`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  FOREIGN KEY (`venue_id`) REFERENCES `venues` (`venueid`) ON DELETE CASCADE,
  FOREIGN KEY (`resolved_by`) REFERENCES `users` (`user_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table structure for table `notifications`
CREATE TABLE IF NOT EXISTS `notifications` (
  `notification_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `type` varchar(50) DEFAULT 'info',
  `action_url` varchar(255) DEFAULT NULL,
  `is_read` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`notification_id`),
  KEY `user_id` (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert sample data

-- Insert sample admin user (password: admin123)
INSERT INTO `users` (`email`, `password`, `role`, `full_name`, `firstname`, `lastname`) VALUES
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'System Administrator', 'System', 'Administrator');

-- Insert sample lecturer (password: lecturer123)
INSERT INTO `users` (`email`, `password`, `role`, `full_name`, `firstname`, `lastname`) VALUES
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'lecturer', 'Dr. John Smith', 'John', 'Smith');

-- Insert sample student (password: student123)
INSERT INTO `users` (`email`, `password`, `role`, `full_name`, `firstname`, `lastname`, `course`, `year`, `stream`) VALUES
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'student', 'Jane Doe', 'Jane', 'Doe', 'Computer Science', '2', 'A');

-- Insert sample venues
INSERT INTO `venues` (`venuename`, `capacity`, `location`, `venuedescription`, `features`, `status`) VALUES
('Lecture Hall A', 150, 'Main Building, Ground Floor', 'Large lecture hall with modern audio-visual equipment', 'Projector, Sound System, Air Conditioning, WiFi', 'available'),
('Computer Lab 1', 40, 'IT Building, 1st Floor', 'Computer laboratory with 40 workstations', 'Computers, Projector, Air Conditioning, WiFi', 'available'),
('Conference Room B', 25, 'Administration Building, 2nd Floor', 'Medium-sized conference room for meetings', 'Projector, Whiteboard, Air Conditioning, WiFi', 'available'),
('Library Study Room', 12, 'Library Building, 3rd Floor', 'Quiet study room for group work', 'Whiteboard, Tables, Chairs, WiFi', 'free'),
('Science Lab 1', 30, 'Science Building, Ground Floor', 'Chemistry laboratory with safety equipment', 'Lab Equipment, Fume Hoods, Safety Shower, WiFi', 'available');

-- Insert sample timetable entries
INSERT INTO `timetables` (`course`, `year`, `stream`, `day`, `start_time`, `end_time`, `type`, `subject_code`, `venue`, `lecturer`) VALUES
('Computer Science', 2, 'A', 'Monday', '08:00:00', '10:00:00', 'Lecture', 'CS201', 'Lecture Hall A', 'Dr. John Smith'),
('Computer Science', 2, 'A', 'Monday', '10:30:00', '12:30:00', 'Lab', 'CS202', 'Computer Lab 1', 'Dr. John Smith'),
('Computer Science', 2, 'A', 'Tuesday', '09:00:00', '11:00:00', 'Lecture', 'CS203', 'Lecture Hall A', 'Dr. Jane Wilson'),
('Computer Science', 2, 'A', 'Wednesday', '14:00:00', '16:00:00', 'Lab', 'CS204', 'Computer Lab 1', 'Dr. John Smith'),
('Computer Science', 2, 'A', 'Thursday', '08:00:00', '10:00:00', 'Lecture', 'CS205', 'Lecture Hall A', 'Dr. Mike Johnson'),
('Computer Science', 2, 'A', 'Friday', '10:00:00', '12:00:00', 'Tutorial', 'CS201', 'Conference Room B', 'Dr. John Smith');

-- Insert sample events
INSERT INTO `events` (`event_name`, `event_description`, `event_date`, `start_time`, `end_time`, `venue_id`, `target_audience`, `target_course`, `target_year`, `target_stream`) VALUES
('Computer Science Seminar', 'Annual computer science department seminar', '2024-02-15', '14:00:00', '16:00:00', 1, 'specific', 'Computer Science', 'all', 'all'),
('University Open Day', 'Open day for prospective students', '2024-02-20', '09:00:00', '17:00:00', 1, 'all', 'all', 'all', 'all'),
('Programming Workshop', 'Hands-on programming workshop for beginners', '2024-02-25', '10:00:00', '15:00:00', 2, 'specific', 'Computer Science', '1', 'all');
